# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration
spring.datasource.url=***********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA / Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=voterSystemSecretKey2023VerySecureAndLongKeyForJwtTokenGeneration
jwt.expiration=86400000

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload-dir=./uploads

# Email Configuration
spring.mail.host=
spring.mail.port=587
spring.mail.username=
spring.mail.password=
spring.mail.protocol=smtp
spring.mail.smtp.auth=true
spring.mail.smtp.starttls.enable=true


# Logging Configuration
logging.level.root=INFO
logging.level.com.votersystem=DEBUG
logging.level.org.springframework.security=DEBUG
# Completely disable file logging
logging.file.name=
logging.pattern.file=
logging.file.max-size=
logging.file.max-history=
# Use only console output
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
# Disable all file appenders
logging.logback.rollingpolicy.file-name-pattern=
logging.logback.rollingpolicy.max-file-size=

# Internationalization
spring.messages.basename=i18n/messages
spring.messages.encoding=UTF-8

# Google Maps Configuration
google.maps.api.key=AIzaSyCelDo4I5cPQ72TfCTQW-arhPZ7ALNcp8w


# Cloudinary Configuration (ADD YOUR CREDENTIALS)
cloudinary.cloud-name=dev7urjca
cloudinary.api-key=845415616497156
cloudinary.api-secret=hE0lWP-B7pPJ5RAogACjkYwDa6A
cloudinary.secure=true

# WebSocket settings
spring.websocket.path-mapping=/ws/location
spring.websocket.allowed-origins=https://api.expengo.com
spring.websocket.heartbeat.time=25000
spring.websocket.heartbeat.timeout=30000

# CORS settings
spring.mvc.cors.allowed-origins=*
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.allow-credentials=true