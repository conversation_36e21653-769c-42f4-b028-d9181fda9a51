{"name": "voter-system-backend", "version": "1.0.0", "description": "Backend API for Voter Registration Management System", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["voter", "registration", "management", "api", "mysql", "express"], "author": "Voter System Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "joi": "^17.11.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "express-session": "^1.17.3", "connect-session-sequelize": "^7.1.7", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "pdf-parse": "^1.1.1", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}